[{"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/index.js": "1", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/App.js": "2", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js": "3", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js": "4", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js": "5", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js": "6", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js": "7", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/About.js": "8", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js": "9", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js": "10", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js": "11", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js": "12", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js": "13", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBentoGrid.js": "14", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js": "15", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Contact.js": "16", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Footer.js": "17", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Blog.js": "18"}, {"size": 254, "mtime": 1753007289437, "results": "19", "hashOfConfig": "20"}, {"size": 489, "mtime": 1753051393064, "results": "21", "hashOfConfig": "20"}, {"size": 1498, "mtime": 1753051213122, "results": "22", "hashOfConfig": "20"}, {"size": 1209, "mtime": 1753007340135, "results": "23", "hashOfConfig": "20"}, {"size": 2321, "mtime": 1753017108809, "results": "24", "hashOfConfig": "20"}, {"size": 779, "mtime": 1753007325960, "results": "25", "hashOfConfig": "20"}, {"size": 1207, "mtime": 1753020237302, "results": "26", "hashOfConfig": "20"}, {"size": 5743, "mtime": 1753048138107, "results": "27", "hashOfConfig": "20"}, {"size": 4333, "mtime": 1753039940190, "results": "28", "hashOfConfig": "20"}, {"size": 2014, "mtime": 1753020756418, "results": "29", "hashOfConfig": "20"}, {"size": 4290, "mtime": 1753046452083, "results": "30", "hashOfConfig": "20"}, {"size": 2085, "mtime": 1753025118440, "results": "31", "hashOfConfig": "20"}, {"size": 7858, "mtime": 1753046452376, "results": "32", "hashOfConfig": "20"}, {"size": 7348, "mtime": 1753048265896, "results": "33", "hashOfConfig": "20"}, {"size": 4890, "mtime": 1753051199944, "results": "34", "hashOfConfig": "20"}, {"size": 4401, "mtime": 1753051256070, "results": "35", "hashOfConfig": "20"}, {"size": 4479, "mtime": 1753051343045, "results": "36", "hashOfConfig": "20"}, {"size": 4193, "mtime": 1753051299729, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "am9nq7", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/index.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/App.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/About.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBentoGrid.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/DarkVeil.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Contact.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Footer.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Blog.js", [], []]
[{"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/index.js": "1", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/App.js": "2", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js": "3", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js": "4", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js": "5", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js": "6", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js": "7", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/About.js": "8", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js": "9", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js": "10", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js": "11", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js": "12", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js": "13", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBentoGrid.js": "14"}, {"size": 254, "mtime": 1753007289437, "results": "15", "hashOfConfig": "16"}, {"size": 315, "mtime": 1753051095722, "results": "17", "hashOfConfig": "16"}, {"size": 1615, "mtime": 1753051097368, "results": "18", "hashOfConfig": "16"}, {"size": 1209, "mtime": 1753007340135, "results": "19", "hashOfConfig": "16"}, {"size": 2321, "mtime": 1753017108809, "results": "20", "hashOfConfig": "16"}, {"size": 779, "mtime": 1753007325960, "results": "21", "hashOfConfig": "16"}, {"size": 1207, "mtime": 1753020237302, "results": "22", "hashOfConfig": "16"}, {"size": 5743, "mtime": 1753048138107, "results": "23", "hashOfConfig": "16"}, {"size": 4333, "mtime": 1753039940190, "results": "24", "hashOfConfig": "16"}, {"size": 2014, "mtime": 1753020756418, "results": "25", "hashOfConfig": "16"}, {"size": 4290, "mtime": 1753046452083, "results": "26", "hashOfConfig": "16"}, {"size": 2085, "mtime": 1753025118440, "results": "27", "hashOfConfig": "16"}, {"size": 7858, "mtime": 1753046452376, "results": "28", "hashOfConfig": "16"}, {"size": 7348, "mtime": 1753048265896, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "am9nq7", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/index.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/App.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/About.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBentoGrid.js", [], []]
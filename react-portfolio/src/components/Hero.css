.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #000;
  overflow: visible;
  padding: 2rem;
}



.hero-content {
  text-align: center;
  z-index: 5;
  max-width: 800px;
}

.hero-title {
  margin-bottom: 2rem;
}

.main-title {
  color: #ffffff;
}

.hero-subtitle {
  margin-bottom: 2rem;
}

.subtitle {
  color: #ccc;
}

.hero-description {
  margin-top: 2rem;
}

.hero-description p {
  font-size: 1.1rem;
  color: #888;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.hero-dark-veil {
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 1rem;
  }
  

  
  .main-title {
    font-size: 4rem !important;
  }
  
  .subtitle {
    font-size: 1.2rem !important;
  }
  
  .hero-description p {
    font-size: 1rem;
  }
  

}

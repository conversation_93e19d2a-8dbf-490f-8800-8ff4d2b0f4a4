.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #000;
  overflow: visible;
  padding: 2rem;
}



.hero-content {
  text-align: center;
  z-index: 5;
  max-width: 800px;
}

.hero-title {
  margin-bottom: 2rem;
}

.main-title {
  color: #ffffff;
}

.hero-subtitle {
  margin-bottom: 2rem;
}

.subtitle {
  color: #ccc;
}

.hero-description {
  margin-top: 2rem;
}

.hero-description p {
  font-size: 1.1rem;
  color: #888;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.hero-dark-veil {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.veil-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  mix-blend-mode: multiply;
}

.veil-layer-1 {
  background: radial-gradient(
    circle at 20% 30%,
    rgba(5, 8, 12, 0.9) 0%,
    rgba(10, 15, 20, 0.7) 30%,
    rgba(0, 0, 0, 0.95) 70%,
    transparent 100%
  );
  animation: veilFloat1 20s ease-in-out infinite;
}

.veil-layer-2 {
  background: radial-gradient(
    circle at 80% 70%,
    rgba(8, 12, 18, 0.8) 0%,
    rgba(15, 20, 25, 0.6) 40%,
    rgba(0, 0, 0, 0.9) 80%,
    transparent 100%
  );
  animation: veilFloat2 25s ease-in-out infinite reverse;
}

.veil-layer-3 {
  background: radial-gradient(
    circle at 50% 50%,
    rgba(3, 5, 8, 0.95) 0%,
    rgba(8, 12, 16, 0.8) 20%,
    rgba(0, 0, 0, 0.98) 60%,
    transparent 100%
  );
  animation: veilFloat3 30s ease-in-out infinite;
}

.veil-noise {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.03;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 2%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 1%),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.08) 0%, transparent 1.5%),
    radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.06) 0%, transparent 1%),
    radial-gradient(circle at 90% 20%, rgba(255, 255, 255, 0.04) 0%, transparent 2%);
  background-size: 200px 200px, 150px 150px, 300px 300px, 250px 250px, 180px 180px;
  animation: noiseMove 15s linear infinite;
}

@keyframes veilFloat1 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translate(-10px, -15px) scale(1.05);
    opacity: 0.9;
  }
  50% {
    transform: translate(15px, -10px) scale(0.95);
    opacity: 0.7;
  }
  75% {
    transform: translate(-5px, 20px) scale(1.02);
    opacity: 0.85;
  }
}

@keyframes veilFloat2 {
  0%, 100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translate(20px, -25px) scale(1.08) rotate(1deg);
    opacity: 0.8;
  }
  66% {
    transform: translate(-15px, 10px) scale(0.92) rotate(-1deg);
    opacity: 0.6;
  }
}

@keyframes veilFloat3 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.9;
  }
  20% {
    transform: translate(-8px, -12px) scale(1.03);
    opacity: 0.95;
  }
  40% {
    transform: translate(12px, -8px) scale(0.97);
    opacity: 0.85;
  }
  60% {
    transform: translate(-6px, 15px) scale(1.01);
    opacity: 0.9;
  }
  80% {
    transform: translate(8px, -5px) scale(0.99);
    opacity: 0.88;
  }
}

@keyframes noiseMove {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-10px, -10px);
  }
  50% {
    transform: translate(10px, -5px);
  }
  75% {
    transform: translate(-5px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 1rem;
  }
  

  
  .main-title {
    font-size: 4rem !important;
  }
  
  .subtitle {
    font-size: 1.2rem !important;
  }
  
  .hero-description p {
    font-size: 1rem;
  }
  

}

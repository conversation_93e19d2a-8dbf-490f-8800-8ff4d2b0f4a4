import React from 'react';
import './DarkVeil.css';

const DarkVeil = ({ className = "" }) => {

  useEffect(() => {
    if (!canvasRef.current) return;

    try {
      // Create renderer
      const renderer = new Renderer({
        canvas: canvasRef.current,
        width: window.innerWidth,
        height: window.innerHeight,
        dpr: Math.min(window.devicePixelRatio, 2),
        alpha: true,
      });
      rendererRef.current = renderer;

      const gl = renderer.gl;
      if (!gl) {
        setWebglSupported(false);
        return;
      }
      gl.clearColor(0, 0, 0, 0);

    // Create camera
    const camera = new Camera(gl, { fov: 35 });
    camera.position.set(0, 0, 5);

    // Create scene
    const scene = new Transform();

    // Vertex shader
    const vertex = `
      attribute vec2 uv;
      attribute vec2 position;
      uniform mat4 modelViewMatrix;
      uniform mat4 projectionMatrix;
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 0.0, 1.0);
      }
    `;

    // Fragment shader
    const fragment = `
      precision highp float;
      uniform float uTime;
      uniform vec2 uResolution;
      varying vec2 vUv;

      // Noise function
      float noise(vec2 p) {
        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
      }

      // Smooth noise
      float smoothNoise(vec2 p) {
        vec2 i = floor(p);
        vec2 f = fract(p);
        f = f * f * (3.0 - 2.0 * f);
        
        float a = noise(i);
        float b = noise(i + vec2(1.0, 0.0));
        float c = noise(i + vec2(0.0, 1.0));
        float d = noise(i + vec2(1.0, 1.0));
        
        return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);
      }

      // Fractal noise
      float fractalNoise(vec2 p) {
        float value = 0.0;
        float amplitude = 0.5;
        float frequency = 1.0;
        
        for(int i = 0; i < 4; i++) {
          value += amplitude * smoothNoise(p * frequency);
          amplitude *= 0.5;
          frequency *= 2.0;
        }
        
        return value;
      }

      void main() {
        vec2 uv = vUv;
        vec2 p = uv * 2.0 - 1.0;
        p.x *= uResolution.x / uResolution.y;
        
        // Create moving noise
        vec2 noiseCoord = p * 3.0 + uTime * 0.1;
        float n1 = fractalNoise(noiseCoord);
        float n2 = fractalNoise(noiseCoord + vec2(100.0));
        
        // Create veil effect
        float veil = smoothstep(0.0, 1.0, length(p) * 0.8);
        veil = pow(veil, 2.0);
        
        // Combine noise with veil
        float finalNoise = mix(n1, n2, sin(uTime * 0.5) * 0.5 + 0.5);
        finalNoise = smoothstep(0.3, 0.7, finalNoise);
        
        // Create dark veil with subtle variations
        float darkness = 1.0 - veil * 0.3;
        darkness *= (1.0 - finalNoise * 0.1);
        
        // Add some color variation
        vec3 color = vec3(0.05, 0.08, 0.12) * darkness;
        color += vec3(0.02, 0.03, 0.05) * finalNoise * (1.0 - veil);
        
        gl_FragColor = vec4(color, darkness * 0.9);
      }
    `;

    // Create geometry
    const geometry = new Plane(gl, {
      width: 2,
      height: 2,
    });

    // Create program with error handling
    let program;
    try {
      program = new Program(gl, {
        vertex,
        fragment,
        uniforms: {
          uTime: { value: 0 },
          uResolution: { value: new Vec2(window.innerWidth, window.innerHeight) },
        },
        transparent: true,
      });
    } catch (error) {
      console.error('Failed to create WebGL program:', error);
      setWebglSupported(false);
      return;
    }

    } catch (error) {
      console.error('WebGL initialization failed:', error);
      setWebglSupported(false);
      return;
    }

    // Create mesh
    const mesh = new Mesh(gl, { geometry, program });
    mesh.setParent(scene);

    // Animation loop
    const animate = (time) => {
      program.uniforms.uTime.value = time * 0.001;
      
      renderer.render({ scene, camera });
      animationRef.current = requestAnimationFrame(animate);
    };

    animate(0);

    // Handle resize
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      renderer.setSize(width, height);
      camera.perspective({ aspect: width / height });
      program.uniforms.uResolution.value.set(width, height);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (rendererRef.current) {
        rendererRef.current.gl.getExtension('WEBGL_lose_context')?.loseContext();
      }
    };
  }, []);

  // Fallback for when WebGL is not supported
  if (!webglSupported) {
    return (
      <div
        className={`dark-veil-fallback ${className}`}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 0,
          background: 'radial-gradient(circle at center, rgba(5, 8, 12, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%)',
        }}
      />
    );
  }

  return (
    <canvas
      ref={canvasRef}
      className={`dark-veil ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 0,
      }}
    />
  );
};

export default DarkVeil;

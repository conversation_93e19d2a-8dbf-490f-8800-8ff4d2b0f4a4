import React, { useRef, useEffect, useState } from 'react';
import './DarkVeil.css';

const DarkVeil = ({ className = "" }) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const updateSize = () => {
      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;
      
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      
      ctx.scale(dpr, dpr);
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';
      
      setDimensions({ width: rect.width, height: rect.height });
    };

    updateSize();
    window.addEventListener('resize', updateSize);

    // Veil particles
    const particles = [];
    const particleCount = 150;

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * dimensions.width,
        y: Math.random() * dimensions.height,
        size: Math.random() * 2 + 0.5,
        speedX: (Math.random() - 0.5) * 0.5,
        speedY: (Math.random() - 0.5) * 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        life: Math.random() * 100,
        maxLife: Math.random() * 100 + 50
      });
    }

    // Animation loop
    let time = 0;
    const animate = () => {
      time += 0.01;
      
      // Clear canvas with dark background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';
      ctx.fillRect(0, 0, dimensions.width, dimensions.height);

      // Create veil effect with gradients
      const gradient1 = ctx.createRadialGradient(
        dimensions.width * 0.3, 
        dimensions.height * 0.3, 
        0,
        dimensions.width * 0.3, 
        dimensions.height * 0.3, 
        dimensions.width * 0.8
      );
      gradient1.addColorStop(0, 'rgba(5, 8, 12, 0.9)');
      gradient1.addColorStop(0.3, 'rgba(10, 15, 20, 0.7)');
      gradient1.addColorStop(0.7, 'rgba(0, 0, 0, 0.95)');
      gradient1.addColorStop(1, 'transparent');

      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, dimensions.width, dimensions.height);

      // Second gradient layer
      const gradient2 = ctx.createRadialGradient(
        dimensions.width * 0.7 + Math.sin(time) * 50, 
        dimensions.height * 0.7 + Math.cos(time * 0.8) * 30, 
        0,
        dimensions.width * 0.7, 
        dimensions.height * 0.7, 
        dimensions.width * 0.6
      );
      gradient2.addColorStop(0, 'rgba(8, 12, 18, 0.8)');
      gradient2.addColorStop(0.4, 'rgba(15, 20, 25, 0.6)');
      gradient2.addColorStop(0.8, 'rgba(0, 0, 0, 0.9)');
      gradient2.addColorStop(1, 'transparent');

      ctx.globalCompositeOperation = 'multiply';
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, dimensions.width, dimensions.height);
      ctx.globalCompositeOperation = 'source-over';

      // Draw and update particles
      particles.forEach((particle, index) => {
        // Update particle
        particle.x += particle.speedX + Math.sin(time + index * 0.1) * 0.1;
        particle.y += particle.speedY + Math.cos(time + index * 0.1) * 0.1;
        particle.life++;

        // Wrap around screen
        if (particle.x < 0) particle.x = dimensions.width;
        if (particle.x > dimensions.width) particle.x = 0;
        if (particle.y < 0) particle.y = dimensions.height;
        if (particle.y > dimensions.height) particle.y = 0;

        // Reset particle if life exceeded
        if (particle.life > particle.maxLife) {
          particle.x = Math.random() * dimensions.width;
          particle.y = Math.random() * dimensions.height;
          particle.life = 0;
          particle.opacity = Math.random() * 0.3 + 0.1;
        }

        // Draw particle
        const alpha = particle.opacity * (1 - particle.life / particle.maxLife);
        ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.1})`;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
      });

      // Add noise texture
      const imageData = ctx.getImageData(0, 0, dimensions.width, dimensions.height);
      const data = imageData.data;
      
      for (let i = 0; i < data.length; i += 4) {
        if (Math.random() < 0.02) {
          const noise = Math.random() * 10;
          data[i] += noise;     // Red
          data[i + 1] += noise; // Green
          data[i + 2] += noise; // Blue
        }
      }
      
      ctx.putImageData(imageData, 0, 0);

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', updateSize);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions.width, dimensions.height]);

  return (
    <div className={`dark-veil-container ${className}`}>
      <canvas
        ref={canvasRef}
        className="dark-veil-canvas"
      />
      <div className="dark-veil-overlay" />
    </div>
  );
};

export default DarkVeil;

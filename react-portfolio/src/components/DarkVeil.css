.dark-veil-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.dark-veil-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.9;
}

.dark-veil-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(
      circle at 50% 50%,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 40%,
      rgba(0, 0, 0, 0.3) 80%,
      rgba(0, 0, 0, 0.5) 100%
    ),
    linear-gradient(
      135deg, 
      rgba(0, 0, 0, 0.2) 0%, 
      transparent 50%
    ),
    linear-gradient(
      45deg, 
      transparent 50%, 
      rgba(0, 0, 0, 0.1) 100%
    );
  opacity: 0.8;
  animation: overlayPulse 8s ease-in-out infinite;
}

@keyframes overlayPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.6;
  }
}

/* Enhanced depth effect */
.dark-veil-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(
      ellipse at 30% 20%,
      rgba(5, 8, 12, 0.4) 0%,
      transparent 60%
    ),
    radial-gradient(
      ellipse at 70% 80%,
      rgba(8, 12, 18, 0.3) 0%,
      transparent 50%
    );
  z-index: 1;
  animation: depthShift 12s ease-in-out infinite;
}

@keyframes depthShift {
  0%, 100% {
    transform: scale(1) translate(0, 0);
    opacity: 0.4;
  }
  25% {
    transform: scale(1.05) translate(-10px, -5px);
    opacity: 0.5;
  }
  50% {
    transform: scale(0.95) translate(5px, -10px);
    opacity: 0.3;
  }
  75% {
    transform: scale(1.02) translate(-5px, 8px);
    opacity: 0.45;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dark-veil-canvas {
    opacity: 0.7;
  }
  
  .dark-veil-overlay {
    opacity: 0.6;
  }
  
  .dark-veil-container::before {
    opacity: 0.3;
  }
}

@media (max-width: 480px) {
  .dark-veil-canvas {
    opacity: 0.5;
  }
  
  .dark-veil-overlay {
    opacity: 0.4;
  }
}

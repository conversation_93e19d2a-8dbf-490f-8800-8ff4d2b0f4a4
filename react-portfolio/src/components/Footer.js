import React from 'react';
import ScrollFloat from './ScrollFloat';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Projects', href: '#projects' },
    { name: 'Blog', href: '#blog' },
    { name: 'Contact', href: '#contact' }
  ];

  const socialLinks = [
    { name: 'GitHub', href: '#', icon: '🐙' },
    { name: 'LinkedIn', href: '#', icon: '💼' },
    { name: 'Twitter', href: '#', icon: '🐦' },
    { name: 'Instagram', href: '#', icon: '📷' }
  ];

  const services = [
    'Web Development',
    'UI/UX Design',
    'Mobile Apps',
    'Consulting',
    'Code Review',
    'Technical Writing'
  ];

  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          <ScrollFloat direction="up" duration={0.8} delay={0.2}>
            <div className="footer-section footer-brand">
              <h3 className="footer-logo">DevDoses</h3>
              <p className="footer-description">
                Creating amazing digital experiences with modern web technologies. 
                Let's build something great together.
              </p>
              <div className="footer-social">
                {socialLinks.map((link, index) => (
                  <a 
                    key={index} 
                    href={link.href} 
                    className="social-icon"
                    aria-label={link.name}
                  >
                    <span>{link.icon}</span>
                  </a>
                ))}
              </div>
            </div>
          </ScrollFloat>

          <ScrollFloat direction="up" duration={0.8} delay={0.4}>
            <div className="footer-section">
              <h4 className="footer-title">Quick Links</h4>
              <ul className="footer-links">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <a href={link.href} className="footer-link">
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </ScrollFloat>

          <ScrollFloat direction="up" duration={0.8} delay={0.6}>
            <div className="footer-section">
              <h4 className="footer-title">Services</h4>
              <ul className="footer-links">
                {services.map((service, index) => (
                  <li key={index}>
                    <span className="footer-service">{service}</span>
                  </li>
                ))}
              </ul>
            </div>
          </ScrollFloat>

          <ScrollFloat direction="up" duration={0.8} delay={0.8}>
            <div className="footer-section">
              <h4 className="footer-title">Get In Touch</h4>
              <div className="footer-contact">
                <div className="contact-item">
                  <span className="contact-icon">📧</span>
                  <span><EMAIL></span>
                </div>
                <div className="contact-item">
                  <span className="contact-icon">📱</span>
                  <span>+995 555 123 456</span>
                </div>
                <div className="contact-item">
                  <span className="contact-icon">📍</span>
                  <span>Tbilisi, Georgia</span>
                </div>
              </div>
            </div>
          </ScrollFloat>
        </div>

        <ScrollFloat direction="up" duration={0.8} delay={1.0}>
          <div className="footer-bottom">
            <div className="footer-divider"></div>
            <div className="footer-bottom-content">
              <p className="footer-copyright">
                © {currentYear} DevDoses. All rights reserved.
              </p>
              <div className="footer-bottom-links">
                <a href="#" className="footer-bottom-link">Privacy Policy</a>
                <a href="#" className="footer-bottom-link">Terms of Service</a>
                <a href="#" className="footer-bottom-link">Cookies</a>
              </div>
            </div>
          </div>
        </ScrollFloat>
      </div>

      {/* Background decoration */}
      <div className="footer-bg">
        <div className="footer-gradient"></div>
      </div>
    </footer>
  );
};

export default Footer;

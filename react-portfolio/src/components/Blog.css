.blog {
  min-height: 100vh;
  padding: 5rem 2rem;
  background: #000;
  position: relative;
  overflow: visible;
}

.blog::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.blog-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.blog-header {
  text-align: center;
  margin-bottom: 4rem;
}

.blog-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-subtitle {
  font-size: 1.2rem;
  color: #888;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.blog-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.blog-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.blog-image {
  width: 60px;
  height: 60px;
  background: rgba(138, 43, 226, 0.2);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(138, 43, 226, 0.3);
}

.blog-emoji {
  font-size: 2rem;
}

.blog-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.blog-category {
  background: rgba(78, 205, 196, 0.2);
  color: rgba(78, 205, 196, 1);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(78, 205, 196, 0.3);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-date {
  color: #888;
  font-size: 0.9rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-card-content {
  flex: 1;
  margin-bottom: 1.5rem;
}

.blog-card-title {
  color: #fff;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.4;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-card-excerpt {
  color: #ccc;
  font-size: 1rem;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.read-time {
  color: #888;
  font-size: 0.9rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.read-more-btn {
  background: none;
  border: none;
  color: rgba(138, 43, 226, 1);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.read-more-btn:hover {
  color: rgba(78, 205, 196, 1);
}

.read-more-btn .arrow {
  transition: transform 0.3s ease;
}

.read-more-btn:hover .arrow {
  transform: translateX(5px);
}

.blog-cta {
  text-align: center;
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.blog-cta h3 {
  color: #fff;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.blog-cta p {
  color: #ccc;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.view-all-btn {
  position: relative;
  padding: 1rem 2rem;
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.8), rgba(78, 205, 196, 0.8));
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(138, 43, 226, 0.3);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.view-all-btn:hover .btn-glow {
  left: 100%;
}

@media (max-width: 768px) {
  .blog {
    padding: 3rem 1rem;
  }

  .blog-title {
    font-size: 2.5rem;
  }

  .blog-subtitle {
    font-size: 1rem;
  }

  .blog-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .blog-card {
    padding: 1.5rem;
  }

  .blog-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .blog-meta {
    align-items: flex-start;
  }

  .blog-cta {
    padding: 2rem 1rem;
  }

  .blog-cta h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .blog {
    padding: 2rem 1rem;
  }

  .blog-title {
    font-size: 2rem;
  }

  .blog-card {
    padding: 1rem;
  }

  .blog-card-title {
    font-size: 1.2rem;
  }
}

import React from 'react';
import ScrollFloat from './ScrollFloat';
import './Blog.css';

const Blog = () => {
  const blogPosts = [
    {
      id: 1,
      title: "Building Modern React Applications",
      excerpt: "Learn how to create scalable and maintainable React applications using the latest best practices and tools.",
      date: "2024-01-15",
      readTime: "5 min read",
      category: "React",
      image: "🚀"
    },
    {
      id: 2,
      title: "The Future of Web Development",
      excerpt: "Exploring emerging technologies and trends that will shape the future of web development in the coming years.",
      date: "2024-01-10",
      readTime: "8 min read",
      category: "Web Dev",
      image: "🌐"
    },
    {
      id: 3,
      title: "CSS Grid vs Flexbox: When to Use What",
      excerpt: "A comprehensive guide to understanding the differences between CSS Grid and Flexbox and when to use each.",
      date: "2024-01-05",
      readTime: "6 min read",
      category: "CSS",
      image: "🎨"
    },
    {
      id: 4,
      title: "JavaScript Performance Optimization",
      excerpt: "Tips and techniques to optimize your JavaScript code for better performance and user experience.",
      date: "2023-12-28",
      readTime: "7 min read",
      category: "JavaScript",
      image: "⚡"
    },
    {
      id: 5,
      title: "Building Responsive Designs",
      excerpt: "Master the art of creating responsive web designs that work perfectly across all devices and screen sizes.",
      date: "2023-12-20",
      readTime: "4 min read",
      category: "Design",
      image: "📱"
    },
    {
      id: 6,
      title: "Node.js Best Practices",
      excerpt: "Essential best practices for building robust and scalable Node.js applications in production environments.",
      date: "2023-12-15",
      readTime: "9 min read",
      category: "Node.js",
      image: "🟢"
    }
  ];

  return (
    <section className="blog" id="blog">
      <div className="blog-container">
        <ScrollFloat direction="up" duration={0.8} delay={0.2}>
          <div className="blog-header">
            <h2 className="blog-title">Latest Blog Posts</h2>
            <p className="blog-subtitle">
              Insights, tutorials, and thoughts on web development
            </p>
          </div>
        </ScrollFloat>

        <div className="blog-grid">
          {blogPosts.map((post, index) => (
            <ScrollFloat 
              key={post.id} 
              direction="up" 
              duration={0.6} 
              delay={0.4 + (index * 0.1)}
            >
              <article className="blog-card">
                <div className="blog-card-header">
                  <div className="blog-image">
                    <span className="blog-emoji">{post.image}</span>
                  </div>
                  <div className="blog-meta">
                    <span className="blog-category">{post.category}</span>
                    <span className="blog-date">{post.date}</span>
                  </div>
                </div>
                
                <div className="blog-card-content">
                  <h3 className="blog-card-title">{post.title}</h3>
                  <p className="blog-card-excerpt">{post.excerpt}</p>
                </div>
                
                <div className="blog-card-footer">
                  <span className="read-time">{post.readTime}</span>
                  <button className="read-more-btn">
                    Read More
                    <span className="arrow">→</span>
                  </button>
                </div>
              </article>
            </ScrollFloat>
          ))}
        </div>

        <ScrollFloat direction="up" duration={0.8} delay={1.0}>
          <div className="blog-cta">
            <h3>Want to read more?</h3>
            <p>Check out my full blog for more articles and tutorials</p>
            <button className="view-all-btn">
              <span>View All Posts</span>
              <div className="btn-glow"></div>
            </button>
          </div>
        </ScrollFloat>
      </div>
    </section>
  );
};

export default Blog;
